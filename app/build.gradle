plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'org.jetbrains.kotlin.plugin.compose'
    id 'com.google.devtools.ksp'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
//    id 'applovin-quality-service'
    id 'kotlin-parcelize'
    id 'org.jetbrains.kotlin.plugin.serialization'
}

//applovin {
//    apiKey "_rHl3dpOz4LdqeQ2F_-Hik_AGduWDsIU9xdMCkdr637qR9qcpTOaLJCogVKA4zip02Qeh2T1apwqJZonNbz3HA"
//}

apply from: "${rootProject.projectDir}/conf4build/copyProject.gradle"
apply from: "${rootProject.projectDir}/conf4build/copyAabWhenBundleFinish.gradle"

def confProp = new Properties()
confProp.load((new FileInputStream(file("${rootProject.projectDir}/conf4build/buildConf.properties"))))

android {
    namespace 'dev.step.app'
    compileSdk 36

    defaultConfig {
        applicationId "dev.step.app"
        minSdk 23
        targetSdk 34
        versionCode confProp.getProperty("versionCode").toInteger()
        versionName confProp.getProperty("versionName")

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }

        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a", "x86", "x86_64"
        }

        buildConfigField("String", "feedbackEmail", "\"${confProp.getProperty("feedbackEmail")}\"")
        buildConfigField("String", "privacyPolicyUrl", "\"${confProp.getProperty("privacyPolicyUrl")}\"")
        buildConfigField("String", "MAX_APP_OPEN_ID", "\"${confProp.getProperty("MAX_APP_OPEN_ID")}\"")
        buildConfigField("String", "MAX_MREC_ID", "\"${confProp.getProperty("MAX_MREC_ID")}\"")
        buildConfigField("String", "MAX_REWARDED_ID", "\"${confProp.getProperty("MAX_REWARDED_ID")}\"")
        buildConfigField("String", "MAX_INTER_ID", "\"${confProp.getProperty("MAX_INTER_ID")}\"")
        buildConfigField("String", "TENJIN_SDK_KEY", "\"${confProp.getProperty("TENJIN_SDK_KEY")}\"")
        buildConfigField("String", "BIGO_APP_ID", "\"${confProp.getProperty("BIGO_APP_ID")}\"")
        buildConfigField("String", "BIGO_APP_OPEN_ID", "\"${confProp.getProperty("BIGO_APP_OPEN_ID")}\"")
    }

    signingConfigs {
        internal {
            keyAlias 'test_sign'
            keyPassword 'test_sign'
            storeFile file("$rootProject.projectDir/test_sign")
            storePassword 'test_sign'
            v2SigningEnabled true
        }
    }
    buildTypes {
        internal {
            minifyEnabled true
            shrinkResources true
            signingConfig signingConfigs.internal
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
        }
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlin {
        jvmToolchain 17
    }
    buildFeatures {
        viewBinding true
        compose true
        buildConfig true
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
//            excludes += 'tec/units/indriya/format/messages.properties'
            excludes += 'META-INF/NOTICE.md'
        }
    }

    applicationVariants.configureEach { variant ->
        variant.sourceSets.java.each {
            it.srcDirs += "build/generated/ksp/${variant.name}/kotlin"
        }
    }
}

dependencies {
    // https://github.com/google/desugar_jdk_libs/blob/master/CHANGELOG.md
    coreLibraryDesugaring "com.android.tools:desugar_jdk_libs:2.1.5"

    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.activity:activity-compose:1.10.1'
//    implementation "androidx.collection:collection-ktx:1.2.0"
//    implementation "androidx.exifinterface:exifinterface:1.3.4"
    implementation 'androidx.startup:startup-runtime:1.2.0'
//    implementation "androidx.datastore:datastore-preferences:1.0.0"
    implementation "androidx.constraintlayout:constraintlayout:2.2.1"
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    //implementation "androidx.profileinstaller:profileinstaller:1.2.0-alpha02"
    implementation 'androidx.work:work-runtime-ktx:2.10.2'

    implementation("io.github.sebaslogen:resaca:4.4.8")
    implementation("io.github.sebaslogen:resacakoin:4.4.8")

    def koinBom = platform('io.insert-koin:koin-bom:4.1.0')
    implementation koinBom
    implementation "io.insert-koin:koin-core"
    implementation "io.insert-koin:koin-android"
    implementation "io.insert-koin:koin-androidx-compose"

    def koinAnnotationsBom = platform('io.insert-koin:koin-annotations-bom:2.1.0')
    implementation koinAnnotationsBom
    implementation "io.insert-koin:koin-annotations"
    ksp "io.insert-koin:koin-ksp-compiler:2.1.0"

    def coroutines = '1.10.2'
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutines"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$coroutines"

    def composeBom = platform('androidx.compose:compose-bom:2025.06.01')
    implementation composeBom
    implementation "androidx.compose.material:material"
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.ui:ui-util"
    implementation "androidx.compose.ui:ui-viewbinding"
    implementation "androidx.compose.ui:ui-tooling"
    implementation "androidx.compose.ui:ui-tooling-preview"
    implementation "androidx.compose.animation:animation"
    implementation "androidx.compose.foundation:foundation"
    implementation "androidx.compose.foundation:foundation-layout"
    implementation "androidx.compose.material:material-icons-extended"

    def room_version = "2.7.2"
    implementation "androidx.room:room-ktx:$room_version"
    implementation "androidx.room:room-runtime:$room_version"
    ksp "androidx.room:room-compiler:$room_version"
//    implementation "androidx.room:room-paging:2.5.0-beta01"

    def lifecycle_version = "2.9.1"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:$lifecycle_version"

    def accompanist = "0.34.0"
    implementation "com.google.accompanist:accompanist-permissions:0.37.3"
    implementation "com.google.accompanist:accompanist-placeholder-material:$accompanist"
    implementation "com.google.accompanist:accompanist-systemuicontroller:0.36.0"
    implementation "com.google.accompanist:accompanist-webview:$accompanist"

    // navigator
    implementation("com.roudikk.guia:guia:1.0.0-beta05")

    def orbit_mvi_version = '10.0.0'
    implementation "org.orbit-mvi:orbit-viewmodel:$orbit_mvi_version"
    implementation "org.orbit-mvi:orbit-compose:$orbit_mvi_version"

    // logger
    implementation('co.touchlab:kermit:2.0.6')

    // java.time
    implementation 'org.jetbrains.kotlinx:kotlinx-datetime:0.6.2'

    // serialization
    implementation('org.jetbrains.kotlinx:kotlinx-serialization-json:1.8.1')

    // mmap kv store
    implementation 'com.tencent:mmkv:1.3.14'

    // image loader
    implementation('io.coil-kt:coil-compose:2.7.0')
    implementation('io.coil-kt:coil-gif:2.7.0')

    // uoms
//    implementation 'org.tenkiv.physikal:physikal-core:2.3.0.2'
//    implementation 'org.tenkiv.physikal:physikal-si-units:2.3.0.2'
//    implementation 'org.tenkiv.physikal:physikal-complete-units:2.3.0.2'
//    //noinspection GradleDependency
//    implementation 'systems.uom:systems-ucum:0.9'

    // systems.uom
    implementation("systems.uom:systems-ucum:2.1")
    implementation("systems.uom:systems-quantity:2.1")
    implementation("systems.uom:systems-common:2.1")
    implementation("systems.uom:systems-unicode:2.1")
    implementation("tech.units:indriya:2.2")
    implementation("si.uom:si-units:2.1")
    implementation("si.uom:si-quantity:2.1")

    implementation "com.airbnb.android:lottie-compose:6.6.7"

    // 3rd composable libs

    // 3rd view libs
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0' // graph & chart
    implementation 'com.zjun:rule-view:0.0.1'
    implementation 'com.github.zj565061763:compose-wheel-picker:1.0.0-alpha14'
    implementation 'com.mikhaellopez:circularprogressbar:3.1.0'

    // gms & ads & analysts
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'
    implementation "com.android.installreferrer:installreferrer:2.2"
    implementation 'com.google.android.gms:play-services-base:18.7.0'
    implementation 'com.google.android.play:review-ktx:2.0.2'

    // firebase
    implementation platform('com.google.firebase:firebase-bom:33.16.0')
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-config-ktx'
    implementation 'com.google.firebase:firebase-messaging'

    implementation 'com.google.android.gms:play-services-ads:24.4.0'


    // TradPlus
    implementation 'com.tradplusad:tradplus:14.3.20.1'
    //noinspection GradleCompatible
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    // Admob
    implementation 'com.tradplusad:tradplus-googlex:2.14.3.20.1'
    // Pangle
    implementation 'com.tradplusad:tradplus-pangle:19.14.3.20.1'
    implementation 'com.pangle.global:pag-sdk:7.2.0.6'
    // Fyber
    implementation 'com.fyber:marketplace-sdk:8.3.7'
    implementation 'com.tradplusad:tradplus-fyber:24.14.3.20.1'
    // Mintegral
    implementation 'com.tradplusad:tradplus-mintegralx_overseas:18.14.3.20.1'
    implementation 'com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71'
    // Liftoff
    implementation 'com.tradplusad:tradplus-vunglex:7.14.3.20.1'
    implementation 'com.vungle:vungle-ads:7.5.0'
    // Verve
    implementation 'net.pubnative:hybid.sdk:3.6.0'
    implementation 'com.tradplusad:tradplus-verve:53.14.3.20.1'
    // Bigo
    implementation 'com.bigossp:bigo-ads:5.3.0'
    implementation 'com.tradplusad:tradplus-bigo:57.14.3.20.1'
    // Cross Promotion
    implementation 'com.tradplusad:tradplus-crosspromotion:27.14.3.20.1'
    // TP Exchange
    implementation 'com.tradplusad:tp_exchange:40.14.3.20.1'


    // applovin
//    implementation 'com.applovin:applovin-sdk:12.4.3'
//    implementation 'com.applovin.mediation:mintegral-adapter:+'
//    implementation 'com.applovin.mediation:fyber-adapter:+'
//    implementation 'com.applovin.mediation:google-ad-manager-adapter:+'
//    implementation 'com.applovin.mediation:google-adapter:+'
//    implementation 'com.applovin.mediation:ironsource-adapter:+'
//    implementation 'com.applovin.mediation:vungle-adapter:+'
//    implementation 'com.applovin.mediation:bytedance-adapter:+'
//    implementation 'com.applovin.mediation:unityads-adapter:+'
//    implementation 'com.applovin.mediation:verve-adapter:+'
//    implementation 'com.bigossp:bigo-ads:4.7.4'
//    implementation 'com.bigossp:max-mediation:4.7.4.0'

    // tenjin
    implementation("com.tenjin:android-sdk:1.16.7")
    implementation 'com.google.code.gson:gson:2.13.1'


    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    androidTestImplementation composeBom
    androidTestImplementation "androidx.compose.ui:ui-test-junit4"
    debugImplementation "androidx.compose.ui:ui-tooling"
}
