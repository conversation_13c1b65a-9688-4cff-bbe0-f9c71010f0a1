package dev.step.app.androidplatform.biz.ad

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalContext
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import dev.step.app.HomeNode
import dev.step.app.NavigateAction
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.androidplatform.biz.ad.admob.interstitial.AdmobInterstitialAdSideEffect
import dev.step.app.androidplatform.biz.ad.admob.interstitial.AdmobInterstitialAdViewModel
import dev.step.app.androidplatform.biz.ad.tradplus.interstitial.TradplusInterstitialAdSideEffect
import dev.step.app.androidplatform.biz.ad.tradplus.interstitial.TradplusInterstitialAdViewModel
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.previousKey
import dev.step.app.ui.dialog.adloading.InterAdLoadingDialog
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

typealias OnTryToShowInterAdAndNavAction = (NavigateAction) -> Unit
typealias OnBackAction = () -> Unit

enum class InterstitialAdProvider {
    ADMOB,
    TRADPLUS
}

@Composable
fun interstitialAdRegister(
    navigator: Navigator,
    checkBackToHome: Boolean = true,
    provider: InterstitialAdProvider
): Pair<OnTryToShowInterAdAndNavAction, OnBackAction> {
    val context = LocalContext.current

    return when (provider) {
        InterstitialAdProvider.ADMOB -> {
            val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
            val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

            LaunchedEffect(Unit) {
                admobInterstitialAdViewModel.registerInterAdEventFlow(this)
            }

            admobInterstitialAdViewModel.collectSideEffect {
                when (it) {
                    is AdmobInterstitialAdSideEffect.NavTo -> {
                        it.navAction(navigator)
                    }

                    AdmobInterstitialAdSideEffect.NavUp -> {
                        navigator.pop()
                    }
                }
            }

            if (admobInterstitialAdViewState.adLoading) {
                InterAdLoadingDialog()
            }

            val onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction = { navAction ->
                admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                    activity = context.findActivity(),
                    navAction = navAction
                )
            }

            val onBackAction = {
                if (navigator.previousKey is HomeNode || !checkBackToHome) {
                    admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(context.findActivity())
                } else {
                    navigator.pop()
                }
                Unit
            }

            onTryToShowInterAdAndNavAction to onBackAction
        }

        InterstitialAdProvider.TRADPLUS -> {
            val tradplusInterstitialAdViewModel: TradplusInterstitialAdViewModel = koinViewModel()
            val tradplusInterstitialAdViewState by tradplusInterstitialAdViewModel.collectAsState()

            LaunchedEffect(Unit) {
                tradplusInterstitialAdViewModel.registerInterAdEventFlow(this)
            }

            tradplusInterstitialAdViewModel.collectSideEffect {
                when (it) {
                    is TradplusInterstitialAdSideEffect.NavTo -> {
                        it.navAction(navigator)
                    }

                    TradplusInterstitialAdSideEffect.NavUp -> {
                        navigator.pop()
                    }
                }
            }

            if (tradplusInterstitialAdViewState.adLoading) {
                InterAdLoadingDialog()
            }

            val onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction = { navAction ->
                tradplusInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                    activity = context.findActivity(),
                    navAction = navAction
                )
            }

            val onBackAction = {
                if (navigator.previousKey is HomeNode || !checkBackToHome) {
                    tradplusInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(context.findActivity())
                } else {
                    navigator.pop()
                }
                Unit
            }

            onTryToShowInterAdAndNavAction to onBackAction
        }
    }
}

@Composable
fun interstitialAdRegister(
    navigator: Navigator,
    checkBackToHome: Boolean = true,
    remoteConfig: FirebaseRemoteConfigHelper = koinInject()
): Pair<OnTryToShowInterAdAndNavAction, OnBackAction> {
    val provider = remoteConfig.getAdProvider()
    return interstitialAdRegister(navigator, checkBackToHome, provider)
}