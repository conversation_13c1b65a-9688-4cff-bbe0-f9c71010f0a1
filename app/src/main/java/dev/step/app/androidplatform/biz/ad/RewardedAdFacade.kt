package dev.step.app.androidplatform.biz.ad

import android.app.Activity
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.rewarded.AdmobRewardedAdManager
import dev.step.app.androidplatform.biz.ad.tradplus.rewarded.TradPlusRewardedAdManager
import dev.step.app.androidplatform.send
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class RewardedAdFacade(
    private val admobManager: AdmobRewardedAdManager,
    private val tradplusManager: TradPlusRewardedAdManager,
    private val remoteConfig: FirebaseRemoteConfigHelper
) {
    companion object {
        private val DEFAULT_PROVIDER = RewardedAdProvider.TRADPLUS
    }

    // Cache to avoid frequent remote config reads
    private var cachedProvider: RewardedAdProvider? = null

    fun getCurrentProvider(): RewardedAdProvider {
        return cachedProvider ?: remoteConfig.getRewardedAdProvider().also { cachedProvider = it }
    }

    fun refreshProvider() {
        cachedProvider = null
    }

    fun tryToLoadAd(activity: Activity) {
        when (getCurrentProvider()) {
            RewardedAdProvider.ADMOB -> admobManager.tryToLoadAd(activity)
            RewardedAdProvider.TRADPLUS -> tradplusManager.tryToLoadAd(activity)
        }
    }

    suspend fun tryToShowAd(
        activity: Activity,
        adPlaceName: String? = null,
        skipDelay: Boolean = false
    ) {
        when (getCurrentProvider()) {
            RewardedAdProvider.ADMOB -> admobManager.tryToShowAd(activity, adPlaceName, skipDelay)
            RewardedAdProvider.TRADPLUS -> tradplusManager.tryToShowAd(activity, adPlaceName, skipDelay)
        }
    }

    fun isAdAvailable(): Boolean {
        return when (getCurrentProvider()) {
            RewardedAdProvider.ADMOB -> admobManager.isAdAvailable()
            RewardedAdProvider.TRADPLUS -> tradplusManager.isAdAvailable()
        }
    }

    // Common interface for ad loading state events
    sealed interface AdLoadingStateEvent {
        data object TimeOut : AdLoadingStateEvent
        data object Loaded : AdLoadingStateEvent
        data object FailedToLoad : AdLoadingStateEvent
    }

    // Common interface for ad show state events  
    sealed interface AdShowStateEvent {
        data object Finish : AdShowStateEvent
        data object Showing : AdShowStateEvent
        data object FailedToShow : AdShowStateEvent
    }

    val adLoadingStateEventFlow = EventFlow<AdLoadingStateEvent>()
    val adShowStateEventFlow = EventFlow<AdShowStateEvent>()

    init {
        // Set up event flow adapters
        setupEventFlowAdapters()
    }

    private fun setupEventFlowAdapters() {
        GlobalScope.launch(Dispatchers.Main) {
            // Adapt AdMob events
            admobManager.adLoadingStateEventFlow.collect { event ->
                val commonEvent = when (event) {
                    AdmobRewardedAdManager.AdLoadingStateEvent.TimeOut -> AdLoadingStateEvent.TimeOut
                    AdmobRewardedAdManager.AdLoadingStateEvent.Loaded -> AdLoadingStateEvent.Loaded
                    AdmobRewardedAdManager.AdLoadingStateEvent.FailedToLoad -> AdLoadingStateEvent.FailedToLoad
                }
                if (getCurrentProvider() == RewardedAdProvider.ADMOB) {
                    adLoadingStateEventFlow.send(commonEvent)
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // Adapt TradPlus events
            tradplusManager.adLoadingStateEventFlow.collect { event ->
                val commonEvent = when (event) {
                    TradPlusRewardedAdManager.AdLoadingStateEvent.TimeOut -> AdLoadingStateEvent.TimeOut
                    TradPlusRewardedAdManager.AdLoadingStateEvent.Loaded -> AdLoadingStateEvent.Loaded
                    TradPlusRewardedAdManager.AdLoadingStateEvent.FailedToLoad -> AdLoadingStateEvent.FailedToLoad
                }
                if (getCurrentProvider() == RewardedAdProvider.TRADPLUS) {
                    adLoadingStateEventFlow.send(commonEvent)
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // Adapt AdMob show events
            admobManager.adShowStateEventFlow.collect { event ->
                val commonEvent = when (event) {
                    AdmobRewardedAdManager.AdShowStateEvent.Finish -> AdShowStateEvent.Finish
                    AdmobRewardedAdManager.AdShowStateEvent.Showing -> AdShowStateEvent.Showing
                    AdmobRewardedAdManager.AdShowStateEvent.FailedToShow -> AdShowStateEvent.FailedToShow
                }
                if (getCurrentProvider() == RewardedAdProvider.ADMOB) {
                    adShowStateEventFlow.send(commonEvent)
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // Adapt TradPlus show events
            tradplusManager.adShowStateEventFlow.collect { event ->
                val commonEvent = when (event) {
                    TradPlusRewardedAdManager.AdShowStateEvent.Finish -> AdShowStateEvent.Finish
                    TradPlusRewardedAdManager.AdShowStateEvent.Showing -> AdShowStateEvent.Showing
                    TradPlusRewardedAdManager.AdShowStateEvent.FailedToShow -> AdShowStateEvent.FailedToShow
                }
                if (getCurrentProvider() == RewardedAdProvider.TRADPLUS) {
                    adShowStateEventFlow.send(commonEvent)
                }
            }
        }
    }

    val adEarnedRewardEventFlow: EventFlow<Boolean>
        get() = when (getCurrentProvider()) {
            RewardedAdProvider.ADMOB -> admobManager.adEarnedRewardEventFlow
            RewardedAdProvider.TRADPLUS -> tradplusManager.adEarnedRewardEventFlow
        }

    fun onDestroy() {
        when (getCurrentProvider()) {
            RewardedAdProvider.TRADPLUS -> tradplusManager.onDestroy()
            RewardedAdProvider.ADMOB -> { /* AdmobRewardedAdManager doesn't have onDestroy */ }
        }
    }
}