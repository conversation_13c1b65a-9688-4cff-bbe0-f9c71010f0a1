package dev.step.app.androidplatform.biz.ad.admob.appopen

import android.app.Activity
import android.content.Context
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.appopen.AppOpenAd
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.FullscreenAdManager
import dev.step.app.androidplatform.biz.ad.admob.AdmobAdUnitIds
import dev.step.app.androidplatform.biz.ad.AppOpenAdLoadingStateEvent
import dev.step.app.androidplatform.biz.ad.AppOpenAdShowStateEvent
import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.send
import dev.step.app.data.kvstore.UserOperateDataKv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single

private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L // 55 minutes

private const val TAG = "AdmobAppOpenAdManager"

@Single
class AdmobAppOpenAdManager(
    private val splashController: SplashHelper,
    private val fullscreenAdManager: FullscreenAdManager
) {
    private var appOpenAd: AppOpenAd? = null
    private var isLoadingAd = false
    private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

    private val adKey = AdmobAdUnitIds.APP_OPEN

    val adLoadingStateEventFlow = EventFlow<AppOpenAdLoadingStateEvent>()
    val adShowStateEventFlow = EventFlow<AppOpenAdShowStateEvent>()

    private var sendLoadingTimeOutJob: Job? = null

    fun tryToLoadAd(activity: Activity) {
        debugLog(tag = TAG) { "tryToLoadAd" }

        if (isLoadingAd) return

        if (isAdAvailable()) {
            debugLog(tag = TAG) { "hasAdAvailable" }

//      adLoadingStateEventFlow.send(AdLoadingStateEvent.SkipToLoad)
            debugLog(tag = TAG) { "send(AdLoadingStateEvent.SkipToLoad)" }
        } else {
            debugLog(tag = TAG) { "noAdAvailable" }

            loadAd(activity)
        }
    }

    private fun loadAd(context: Context) {
        sendLoadingTimeOutJob?.cancel()
        sendLoadingTimeOutJob = null
        sendLoadingTimeOutJob = GlobalScope.launch(Dispatchers.Default) {
            delay(8_000)
            debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
            adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.TimeOut)
        }

        if (isLoadingAd) {
            debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
            return
        }

        debugLog(tag = TAG) { "loadAd" }

        isLoadingAd = true
        AppOpenAd.load(
            context,
            adKey,
            AdRequest.Builder().build(),
            object : AppOpenAd.AppOpenAdLoadCallback() {
                override fun onAdLoaded(ad: AppOpenAd) {
                    debugLog(tag = TAG) { "onAdLoaded" }

                    ad.onPaidEventListener = OnPaidEventListener { adValue ->
                        val adSourceName = ad.responseInfo.loadedAdapterResponseInfo?.adSourceName
                        val adFormat = "app_open"
                        val adUnitId = ad.adUnitId

                        AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
                        AnalyticsLogEvent.recordAdImpressionRevenue(
                            adValue,
                            adSourceName,
                            adFormat,
                            ""
                        )
                        AnalyticsLogEvent.recordAdImpression(
                            adValue,
                            adSourceName,
                            adFormat,
                            adUnitId
                        )
                        AnalyticsLogEvent.tenjinEventAdImpressionAdMob(adValue, ad)

                    }

                    appOpenAd = ad
                    isLoadingAd = false
                    latestLoadAdSuccessInstant = nowInstant()

                    sendLoadingTimeOutJob?.cancel()
                    sendLoadingTimeOutJob = null
                    adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.Loaded)
                    debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

                    logEventRecord("ad_app_open_load_success")
                }

                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    debugLog(tag = TAG) { "onAdFailedToLoad" }

                    isLoadingAd = false

                    sendLoadingTimeOutJob?.cancel()
                    sendLoadingTimeOutJob = null
                    adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.FailedToLoad)
                }
            }
        )

        logEventRecord("ad_app_open_load")
    }

    private fun isAdAvailable(): Boolean {
        return appOpenAd != null && checkAdIsValidAtCachePeriod()
    }

    private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
        val secondsDifference: Long =
            nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
        return secondsDifference < adCachePeriodSeconds
    }

    private fun showAd(activity: Activity) {
        debugLog(tag = TAG) { "showAd" }

        logEventRecord("ad_app_open_show")

        appOpenAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdDismissedFullScreenContent() {
                appOpenAd = null
                loadAd(activity)
                adShowStateEventFlow.send(AppOpenAdShowStateEvent.Finish)
            }

            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                debugLog(tag = TAG) { "onAdFailedToShowFullScreenContent" }
                appOpenAd = null
                loadAd(activity)
                adShowStateEventFlow.send(AppOpenAdShowStateEvent.FailedToShow)
            }

            override fun onAdShowedFullScreenContent() {
                fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()
                adShowStateEventFlow.send(AppOpenAdShowStateEvent.Showing)
                debugLog(tag = TAG) { "onAdShowedFullScreenContent" }
            }

            override fun onAdClicked() {
                splashController.doSkipSplash(true)
                logEventRecord("ad_app_open_click")
            }

            override fun onAdImpression() {
                logEventRecord("ad_app_open_impress")
            }
        }

        appOpenAd?.show(activity)
    }

    suspend fun tryToShowAd(
        activity: Activity,
        immediate: Boolean = false
    ) = withContext(Dispatchers.Main.immediate) {
        debugLog(tag = TAG) { "tryToShowAd" }

        if (fullscreenAdManager.isAdShowTimeInShowInterval()) {
            debugLog(tag = TAG) { "isAdShowTimeInShowInterval" }
            adShowStateEventFlow.send(AppOpenAdShowStateEvent.SkipToShow)
        } else { // over the show interval, need to show ad
            debugLog(tag = TAG) { "over the show interval, need to show ad" }
            if (isAdAvailable()) { // cache available
                debugLog(tag = TAG) { "cache available" }
                if (!immediate) {
                    delay(1_000)
                }
                showAd(activity)
            } else { // cache not available
                debugLog(tag = TAG) { "cache not available" }
                loadAd(activity)
            }
        }
    }
}