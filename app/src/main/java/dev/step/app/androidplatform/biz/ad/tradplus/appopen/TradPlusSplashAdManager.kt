package dev.step.app.androidplatform.biz.ad.tradplus.appopen

import android.app.Activity
import android.content.Context
import android.view.ViewGroup
import com.tradplus.ads.open.TradPlusSdk
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.base.bean.TPBaseAd
import com.tradplus.ads.open.splash.SplashAdListener
import com.tradplus.ads.open.splash.TPSplash
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.FullscreenAdManager
import dev.step.app.androidplatform.biz.ad.tradplus.TradplusAdUnitIds
import dev.step.app.androidplatform.biz.ad.AdLoadingStateEvent
import dev.step.app.androidplatform.biz.ad.AdShowStateEvent
import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.send
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single

private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L // 55 minutes
private const val AD_TIMEOUT_SECONDS = 8L
private const val TAG = "TradPlusSplashAdManager"

@Single
class TradPlusSplashAdManager(
    private val splashController: SplashHelper,
    private val fullscreenAdManager: FullscreenAdManager
) {
    private var tpSplash: TPSplash? = null
    private var isLoadingAd = false
    private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

    private val adKey = TradplusAdUnitIds.APP_OPEN

    val adLoadingStateEventFlow = EventFlow<AppOpenAdLoadingStateEvent>()
    val adShowStateEventFlow = EventFlow<AppOpenAdShowStateEvent>()

    private var loadingTimeoutJob: Job? = null

    fun tryToLoadAd(activity: Activity) {
        debugLog(tag = TAG) { "tryToLoadAd" }

        if (isLoadingAd) {
            debugLog(tag = TAG) { "Already loading ad, skipping" }
            return
        }

        if (isAdAvailable()) {
            debugLog(tag = TAG) { "Ad already available, skipping load" }
            adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.Loaded)
        } else {
            debugLog(tag = TAG) { "No ad available, loading new ad" }
            loadAd(activity)
        }
    }

    private fun loadAd(context: Context) {
        debugLog(tag = TAG) { "loadAd - TradPlus Splash" }

        // Cancel any existing timeout job
        loadingTimeoutJob?.cancel()
        loadingTimeoutJob = null

        // Set up timeout job
        loadingTimeoutJob = GlobalScope.launch(Dispatchers.Default) {
            delay(AD_TIMEOUT_SECONDS * 1000)
            if (isLoadingAd) {
                debugLog(tag = TAG) { "Ad loading timeout" }
                isLoadingAd = false
                adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.TimeOut)
            }
        }

        if (isLoadingAd) {
            debugLog(tag = TAG) { "Already loading ad, returning" }
            return
        }

        // Check if TradPlus SDK is initialized
        if (!TradPlusSdk.getIsInit()) {
            debugLog(tag = TAG) { "TradPlus SDK not initialized, cannot load ad" }
            isLoadingAd = false
            loadingTimeoutJob?.cancel()
            loadingTimeoutJob = null
            adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.FailedToLoad)
            return
        }

        isLoadingAd = true

        // Initialize TPSplash
        tpSplash = TPSplash(context, adKey)

        // Set ad listener
        tpSplash?.setAdListener(object : SplashAdListener() {
            override fun onAdLoaded(adInfo: TPAdInfo?, tpBaseAd: TPBaseAd?) {
                debugLog(tag = TAG) { "onAdLoaded" }
                isLoadingAd = false
                latestLoadAdSuccessInstant = nowInstant()

                loadingTimeoutJob?.cancel()
                loadingTimeoutJob = null
                adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.Loaded)

                logEventRecord("ad_splash_load_success")
            }

            override fun onAdLoadFailed(error: TPAdError?) {
                debugLog(tag = TAG) { "onAdLoadFailed: ${error?.errorMsg}" }
                isLoadingAd = false

                loadingTimeoutJob?.cancel()
                loadingTimeoutJob = null
                adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.FailedToLoad)

                logEventRecord("ad_splash_load_failed")
            }

            override fun onAdClicked(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdClicked" }
                logEventRecord("ad_splash_click")
                splashController.doSkipSplash(true)
            }

            override fun onAdImpression(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdImpression" }
                logEventRecord("ad_splash_impress")

                fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()
                adShowStateEventFlow.send(AppOpenAdShowStateEvent.Showing)

                // Record revenue analytics
                adInfo?.let { info ->
                    val adSourceName = info.adSourceName
                    val adFormat = "splash"
                    val adUnitId = adKey

                    AnalyticsLogEvent.recordAdImpression(
                        null, // adValue not available in TradPlus callback
                        adSourceName,
                        adFormat,
                        adUnitId
                    )
                }
            }

            override fun onAdClosed(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdClosed" }
                tpSplash?.onDestroy()
                tpSplash = null
                adShowStateEventFlow.send(AppOpenAdShowStateEvent.Finish)
                
                // Preload next ad
                loadAd(context)
            }
        })

        // Load the ad
        tpSplash?.loadAd(null)
        logEventRecord("ad_splash_load")
    }

    fun isAdAvailable(): Boolean {
        return tpSplash?.isReady() == true && checkAdIsValidAtCachePeriod()
    }

    private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
        val secondsDifference: Long = nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
        return secondsDifference < adCachePeriodSeconds
    }

    fun showAd(activity: Activity) {
        debugLog(tag = TAG) { "showAd - TradPlus Splash" }

        if (!isAdAvailable()) {
            debugLog(tag = TAG) { "Ad not available, cannot show" }
            adShowStateEventFlow.send(AppOpenAdShowStateEvent.FailedToShow)
            return
        }

        logEventRecord("ad_splash_show")

        try {
            // Find the content view to show splash ad
            val contentView = activity.findViewById<ViewGroup>(android.R.id.content)
            tpSplash?.showAd(contentView)
        } catch (e: Exception) {
            debugLog(tag = TAG) { "Error showing splash ad: ${e.message}" }
            adShowStateEventFlow.send(AppOpenAdShowStateEvent.FailedToShow)
        }
    }

    suspend fun tryToShowAd(activity: Activity, immediate: Boolean = false) = withContext(Dispatchers.Main.immediate) {
        debugLog(tag = TAG) { "tryToShowAd, immediate: $immediate" }

        if (fullscreenAdManager.isAdShowTimeInShowInterval()) {
            debugLog(tag = TAG) { "Ad show time is in show interval, skipping" }
            adShowStateEventFlow.send(AppOpenAdShowStateEvent.SkipToShow)
            return@withContext
        }

        if (isAdAvailable()) {
            debugLog(tag = TAG) { "Ad available, showing" }
            if (!immediate) {
                delay(1000) // Small delay before showing
            }
            showAd(activity)
        } else {
            debugLog(tag = TAG) { "Ad not available, loading first" }
            loadAd(activity)
        }
    }

    fun onDestroy() {
        debugLog(tag = TAG) { "onDestroy" }
        loadingTimeoutJob?.cancel()
        loadingTimeoutJob = null
        tpSplash?.onDestroy()
        tpSplash = null
        isLoadingAd = false
    }
}