package dev.step.app.androidplatform.biz.ad.rewarded

import android.app.Activity
import android.content.Context
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.RewardedAdFacade
import dev.step.app.androidplatform.biz.ad.AdLoadingStateEvent
import dev.step.app.androidplatform.biz.ad.AdShowStateEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.send
import dev.step.app.data.kvstore.UserOperateDataKv
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import org.koin.core.annotation.Single


class RewardedAdManager(
    private val context: Context,
    private val splashHelper: Splash<PERSON>elper,
    private val userOperateDataKv: UserOperateDataKv,
    private val rewardedAdFacade: RewardedAdFacade,
) {

    @Suppress("PrivatePropertyName")
    private val TAG = "RewardedAdManager"

    private var isShowAdWithTipsDialog = false
    private val currentActiveAdFrom = MutableStateFlow("")
    private var tryToShowRewardedAdWithTipsDialogTimeoutJob: Job? = null

    init {
        // 监听RewardedAdFacade的事件并转换为原有的事件流
        setupEventFlowAdapters()
    }

    private fun setupEventFlowAdapters() {
        GlobalScope.launch(Dispatchers.Main) {
            // 监听广告加载状态
            rewardedAdFacade.adLoadingStateEventFlow.collect { event ->
                when (event) {
                    RewardedAdFacade.AdLoadingStateEvent.Loaded -> {
                        debugLog(tag = TAG) { "RewardedAdFacade.AdLoadingStateEvent.Loaded" }
                        
                        if (isShowAdWithTipsDialog) {
                            debugLog(tag = TAG) { "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog) onAdLoaded" }
                            
                            tryToShowRewardedAdWithTipsDialogTimeoutJob?.cancel()
                            rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)
                        }
                    }
                    
                    RewardedAdFacade.AdLoadingStateEvent.FailedToLoad -> {
                        debugLog(tag = TAG) { "RewardedAdFacade.AdLoadingStateEvent.FailedToLoad" }
                        
                        val adFrom = currentActiveAdFrom.first()
                        if (adFrom.isNotEmpty()) {
                            debugLog(tag = TAG) { "ad_${adFrom}_incentive_load_failed" }
                            logEventRecord("ad_${adFrom}_incentive_load_failed")
                        }
                        currentActiveAdFrom.update { "" }
                    }
                    
                    RewardedAdFacade.AdLoadingStateEvent.TimeOut -> {
                        debugLog(tag = TAG) { "RewardedAdFacade.AdLoadingStateEvent.TimeOut" }
                        rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)
                    }
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // 监听广告展示状态
            rewardedAdFacade.adShowStateEventFlow.collect { event ->
                when (event) {
                    RewardedAdFacade.AdShowStateEvent.Showing -> {
                        debugLog(tag = TAG) { "RewardedAdFacade.AdShowStateEvent.Showing" }
                        
                        splashHelper.doSkipSplash(true)
                        
                        val adFrom = currentActiveAdFrom.first()
                        if (adFrom.isNotEmpty()) {
                            debugLog(tag = TAG) { "ad_${adFrom}_incentive_impress" }
                            logEventRecord("ad_${adFrom}_incentive_impress")
                            logEventRecord("ad_incentive_impress")
                        }
                    }
                    
                    RewardedAdFacade.AdShowStateEvent.Finish -> {
                        debugLog(tag = TAG) { "RewardedAdFacade.AdShowStateEvent.Finish" }
                        
                        val adFrom = currentActiveAdFrom.first()
                        debugLog(tag = TAG) { "ad_${adFrom}_incentive_close" }
                        logEventRecord("ad_${adFrom}_incentive_close")
                        
                        if (isShowAdWithTipsDialog) {
                            rewardedLoadingDialogFinishEventFlow.send(Unit)
                            isShowAdWithTipsDialog = false
                        }
                    }
                    
                    RewardedAdFacade.AdShowStateEvent.FailedToShow -> {
                        debugLog(tag = TAG) { "RewardedAdFacade.AdShowStateEvent.FailedToShow" }
                        
                        val adFrom = currentActiveAdFrom.first()
                        if (adFrom.isNotEmpty()) {
                            debugLog(tag = TAG) { "ad_${adFrom}_incentive_display_failed" }
                            logEventRecord("ad_${adFrom}_incentive_display_failed")
                        }
                    }
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // 监听广告奖励状态
            rewardedAdFacade.adEarnedRewardEventFlow.collect { earned ->
                if (earned) {
                    debugLog(tag = TAG) { "RewardedAdFacade.adEarnedRewardEventFlow: earned reward" }
                    
                    val adFrom = currentActiveAdFrom.first()
                    debugLog(tag = TAG) { "ad_${adFrom}_incentive_rewarded" }
                    logEventRecord("ad_${adFrom}_incentive_rewarded")
                }
            }
        }
    }

    fun hasInit(): Boolean {
        // RewardedAdFacade manages initialization internally
        return true
    }

    fun initIfNeed(activity: Activity) {
        // Preload ads through the facade
        rewardedAdFacade.tryToLoadAd(activity)
    }

    fun showRewardedAd() {
        GlobalScope.launch(Dispatchers.Main) {
            val activity = dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
            if (activity != null) {
                val adFrom = currentActiveAdFrom.first()
                rewardedAdFacade.tryToShowAd(activity, adFrom, skipDelay = true)
            }
        }
    }

    fun tryToLoadRewardedAd() {
        val activity = dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
        if (activity != null) {
            rewardedAdFacade.tryToLoadAd(activity)
        }
    }

    fun tryToShowRewardedLoadingDialog(
        from: String? = null,
        instantlyLoad: Boolean = !userOperateDataKv.tenjinAttr.isOrganic()
    ) {
        debugLog(tag = TAG) { "tryToShowRewardedLoadingDialog from: $from" }

        GlobalScope.launch(Dispatchers.Main) {
            isShowAdWithTipsDialog = true

            debugLog(tag = TAG) { "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.StartShow)" }
            rewardedLoadingDialogEventFlow.send(
                RewardedLoadingDialogEvent.StartShow(instantlyLoad)
            )

            from?.let {
                debugLog(tag = TAG) { "ad_${from}_incentive_show" }
                logEventRecord("ad_${from}_incentive_show")
                logEventRecord("ad_incentive_show")
                currentActiveAdFrom.emit(from)
            }

            val activity = dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
            if (activity != null) {
                // 检查是否有可用的广告缓存
                if (rewardedAdFacade.isAdAvailable()) {
                    delay(1000)
                    debugLog(tag = TAG) { "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)" }
                    rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)
                } else {
                    // 没有缓存，开始加载广告
                    rewardedAdFacade.tryToLoadAd(activity)

                    // 设置超时处理
                    tryToShowRewardedAdWithTipsDialogTimeoutJob?.cancel()
                    tryToShowRewardedAdWithTipsDialogTimeoutJob = null
                    tryToShowRewardedAdWithTipsDialogTimeoutJob = launch {
                        delay(20_000) // 20秒超时
                        debugLog(tag = TAG) { "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)" }
                        rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)
                    }
                }
            }
        }
    }
}
