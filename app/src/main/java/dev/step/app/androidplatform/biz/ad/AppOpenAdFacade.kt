package dev.step.app.androidplatform.biz.ad

import android.app.Activity
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.appopen.AdmobAppOpenAdManager
import dev.step.app.androidplatform.biz.ad.tradplus.appopen.TradPlusSplashAdManager
import org.koin.core.annotation.Single

@Single
class AppOpenAdFacade(
    private val admobManager: AdmobAppOpenAdManager,
    private val tradplusSplashManager: TradPlusSplashAdManager,
    private val remoteConfig: FirebaseRemoteConfigHelper
) {
    companion object {
        private val DEFAULT_PROVIDER = AdProvider.TRADPLUS
    }

    // Cache to avoid frequent remote config reads
    private var cachedProvider: AdProvider? = null

    fun getCurrentProvider(): AdProvider {
        return cachedProvider ?: remoteConfig.getAdProvider().also { cachedProvider = it }
    }

    fun refreshProvider() {
        cachedProvider = null
    }

    fun tryToLoadAd(activity: Activity) {
        when (getCurrentProvider()) {
            AppOpenAdProvider.ADMOB -> admobManager.tryToLoadAd(activity)
            AppOpenAdProvider.TRADPLUS -> tradplusSplashManager.tryToLoadAd(activity)
        }
    }

    suspend fun tryToShowAd(
        activity: Activity,
        immediate: Boolean = false
    ) {
        when (getCurrentProvider()) {
            AppOpenAdProvider.ADMOB -> admobManager.tryToShowAd(activity, immediate)
            AppOpenAdProvider.TRADPLUS -> tradplusSplashManager.tryToShowAd(activity, immediate)
        }
    }

    val adLoadingStateEventFlow: EventFlow<AppOpenAdLoadingStateEvent>
        get() = when (getCurrentProvider()) {
            AppOpenAdProvider.ADMOB -> admobManager.adLoadingStateEventFlow
            AppOpenAdProvider.TRADPLUS -> tradplusSplashManager.adLoadingStateEventFlow
        }

    val adShowStateEventFlow: EventFlow<AppOpenAdShowStateEvent>
        get() = when (getCurrentProvider()) {
            AppOpenAdProvider.ADMOB -> admobManager.adShowStateEventFlow
            AppOpenAdProvider.TRADPLUS -> tradplusSplashManager.adShowStateEventFlow
        }

    fun onDestroy() {
        // Only TradPlus manager has onDestroy method currently
        when (getCurrentProvider()) {
            AppOpenAdProvider.TRADPLUS -> tradplusSplashManager.onDestroy()
            AppOpenAdProvider.ADMOB -> { /* AdmobAppOpenAdManager doesn't have onDestroy */ }
        }
    }
}