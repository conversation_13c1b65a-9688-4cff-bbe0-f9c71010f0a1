package dev.step.app.androidplatform.biz.ad.tradplus.rewarded

import android.app.Activity
import android.content.Context
import com.tradplus.ads.open.TradPlusSdk
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.base.bean.TPBaseAd
import com.tradplus.ads.open.reward.RewardAdListener
import com.tradplus.ads.open.reward.TPReward
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.tradplus.TradplusAdUnitIds
import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.send
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private const val AD_CACHE_PERIOD_SECONDS = 45 * 60L
private const val AD_LOADING_TIMEOUT_SECONDS = 20

private const val TAG = "TradPlusRewardedAdManager"

@Single
class TradPlusRewardedAdManager(
    private val splashController: SplashHelper,
) {

    private val adKey = TradplusAdUnitIds.REWARDED

    private var tpReward: TPReward? = null
    private var isLoadingAd = false
    private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

    private val latestActiveAdPlaceNameFlow = MutableStateFlow<String?>(null)

    sealed interface AdLoadingStateEvent {
        data object TimeOut : AdLoadingStateEvent
        data object Loaded : AdLoadingStateEvent
        data object FailedToLoad : AdLoadingStateEvent
    }

    sealed interface AdShowStateEvent {
        data object Finish : AdShowStateEvent
        data object Showing : AdShowStateEvent
        data object FailedToShow : AdShowStateEvent
    }

    val adLoadingStateEventFlow = EventFlow<AdLoadingStateEvent>()
    val adShowStateEventFlow = EventFlow<AdShowStateEvent>()
    val adEarnedRewardEventFlow = EventFlow<Boolean>()
    private val adEarnedRewardStateFlow = MutableStateFlow(false)

    private var sendLoadingTimeOutJob: Job? = null

    fun tryToLoadAd(activity: Activity) {
        debugLog(tag = TAG) { "tryToLoadAd" }

        if (isLoadingAd) {
            debugLog(tag = TAG) { "Already loading ad, skipping" }
            return
        }

        if (isAdAvailable()) {
            debugLog(tag = TAG) { "hasAdAvailable" }
            debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }
            adLoadingStateEventFlow.send(AdLoadingStateEvent.Loaded)
        } else {
            debugLog(tag = TAG) { "noAdAvailable" }
            loadAd(activity)
        }
    }

    private fun loadAd(context: Context) {
        debugLog(tag = TAG) { "loadAd - TradPlus Rewarded" }

        sendLoadingTimeOutJob?.cancel()
        sendLoadingTimeOutJob = null
        sendLoadingTimeOutJob = GlobalScope.launch(Dispatchers.Default) {
            delay(AD_LOADING_TIMEOUT_SECONDS.toDuration(DurationUnit.SECONDS))
            debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
            adLoadingStateEventFlow.send(AdLoadingStateEvent.TimeOut)
        }

        if (isLoadingAd) {
            debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
            return
        }

        // Check if TradPlus SDK is initialized
        if (!TradPlusSdk.getIsInit()) {
            debugLog(tag = TAG) { "TradPlus SDK not initialized, cannot load ad" }
            isLoadingAd = false
            sendLoadingTimeOutJob?.cancel()
            sendLoadingTimeOutJob = null
            adLoadingStateEventFlow.send(AdLoadingStateEvent.FailedToLoad)
            return
        }

        isLoadingAd = true

        // Initialize TPReward
        tpReward = TPReward(context, adKey)

        // Set ad listener
        tpReward?.setAdListener(object : RewardAdListener {
            override fun onAdLoaded(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdLoaded" }
                
                isLoadingAd = false
                latestLoadAdSuccessInstant = nowInstant()

                sendLoadingTimeOutJob?.cancel()
                sendLoadingTimeOutJob = null
                adLoadingStateEventFlow.send(AdLoadingStateEvent.Loaded)
                debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

                logEventRecord("ad_rewarded_load_success")
            }


            override fun onAdClicked(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdClicked" }
                splashController.doSkipSplash(true)
                logEventRecord("ad_rewarded_click")
            }

            override fun onAdImpression(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdImpression" }
                logEventRecord("ad_rewarded_impress")
                
                adShowStateEventFlow.send(AdShowStateEvent.Showing)

                // Record revenue analytics
                adInfo?.let { info ->
                    val adSourceName = info.adSourceName
                    val adFormat = "rewarded"
                    val adUnitId = adKey

                    // Revenue tracking - TradPlus doesn't provide direct revenue data in callbacks
                    // But we can still track the impression event
                    AnalyticsLogEvent.recordAdImpression(
                        null, // adValue not available in TradPlus callback
                        adSourceName,
                        adFormat,
                        adUnitId
                    )

                    // Track revenue by place name if available
                    latestActiveAdPlaceNameFlow.value?.let { placeName ->
                        AnalyticsLogEvent.recordAdImpressionRevenue(
                            null, // adValue not available
                            adSourceName,
                            adFormat,
                            placeName
                        )
                    }
                }
            }

            override fun onAdClosed(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdClosed" }
                
                // Clean up and preload next ad
                tpReward?.onDestroy()
                tpReward = null
                loadAd(context)
                
                adShowStateEventFlow.send(AdShowStateEvent.Finish)
                adEarnedRewardEventFlow.send(adEarnedRewardStateFlow.value)
                adEarnedRewardStateFlow.update { false }
            }

            override fun onAdFailed(error: TPAdError?) {
                debugLog(tag = TAG) { "onAdFailed: ${error?.errorMsg}" }
                
                tpReward?.onDestroy()
                tpReward = null
                loadAd(context)
                
                adShowStateEventFlow.send(AdShowStateEvent.FailedToShow)
                adEarnedRewardEventFlow.send(false)
                adEarnedRewardStateFlow.update { false }
            }

            override fun onAdVideoStart(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdVideoStart" }
            }

            override fun onAdVideoEnd(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdVideoEnd" }
            }

            override fun onAdVideoError(adInfo: TPAdInfo?, error: TPAdError?) {
                debugLog(tag = TAG) { "onAdVideoError: ${error?.errorMsg}" }
            }

            override fun onAdReward(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdReward - User earned reward" }
                adEarnedRewardStateFlow.update { true }
                logEventRecord("ad_rewarded_earned")
            }
        })

        // Load the ad
        tpReward?.loadAd()
        logEventRecord("ad_rewarded_load")
    }

    fun isAdAvailable(): Boolean {
        return tpReward?.isReady() == true && checkAdIsValidAtCachePeriod()
    }

    private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
        val secondsDifference: Long = nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
        return secondsDifference < adCachePeriodSeconds
    }

    private fun showAd(activity: Activity) {
        debugLog(tag = TAG) { "showAd - TradPlus Rewarded" }

        if (!isAdAvailable()) {
            debugLog(tag = TAG) { "Ad not available, cannot show" }
            adShowStateEventFlow.send(AdShowStateEvent.FailedToShow)
            return
        }

        logEventRecord("ad_rewarded_show")

        try {
            tpReward?.showAd(activity, null)
        } catch (e: Exception) {
            debugLog(tag = TAG) { "Error showing rewarded ad: ${e.message}" }
            adShowStateEventFlow.send(AdShowStateEvent.FailedToShow)
        }
    }

    suspend fun tryToShowAd(
        activity: Activity,
        adPlaceName: String? = null,
        skipDelay: Boolean = false,
    ) = withContext(Dispatchers.Main.immediate) {
        debugLog(tag = TAG) { "tryToShowAd" }

        adEarnedRewardStateFlow.update { false }

        adPlaceName?.let {
            latestActiveAdPlaceNameFlow.update { "rewarded_$adPlaceName" }
        }

        if (isAdAvailable()) { // cache available
            debugLog(tag = TAG) { "cache available" }
            if (!skipDelay) {
                delay(1_000)
            }
            showAd(activity)
        } else { // cache not available
            debugLog(tag = TAG) { "cache not available" }
            loadAd(activity)
        }
    }

    fun onDestroy() {
        debugLog(tag = TAG) { "onDestroy" }
        sendLoadingTimeOutJob?.cancel()
        sendLoadingTimeOutJob = null
        tpReward?.onDestroy()
        tpReward = null
        isLoadingAd = false
    }
}