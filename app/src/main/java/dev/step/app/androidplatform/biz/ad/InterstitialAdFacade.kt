package dev.step.app.androidplatform.biz.ad

import android.app.Activity
import android.content.Context
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.interstitial.AdmobInterstitialAdManager
import dev.step.app.androidplatform.biz.ad.tradplus.interstitial.TradplusInterstitialAdManager
import kotlinx.coroutines.flow.MutableStateFlow
import org.koin.core.annotation.Single

@Single
class InterstitialAdFacade(
    private val admobManager: AdmobInterstitialAdManager,
    private val tradplusManager: TradplusInterstitialAdManager,
    private val remoteConfig: FirebaseRemoteConfigHelper
) {
    companion object {
        private val DEFAULT_PROVIDER = InterstitialAdProvider.TRADPLUS
    }

    // Cache to avoid frequent remote config reads
    private var cachedProvider: InterstitialAdProvider? = null

    fun getCurrentProvider(): InterstitialAdProvider {
        return cachedProvider ?: remoteConfig.getInterstitialAdProvider().also { cachedProvider = it }
    }

    fun refreshProvider() {
        cachedProvider = null
    }

    fun tryToLoadAd(activity: Activity) {
        when (getCurrentProvider()) {
            InterstitialAdProvider.ADMOB -> admobManager.tryToLoadAd(activity)
            InterstitialAdProvider.TRADPLUS -> tradplusManager.tryToLoadAd(activity)
        }
    }

    fun tryToLoadAd(context: Context) {
        when (getCurrentProvider()) {
            InterstitialAdProvider.ADMOB -> admobManager.tryToLoadAd(context as Activity)
            InterstitialAdProvider.TRADPLUS -> tradplusManager.tryToLoadAd(context)
        }
    }

    suspend fun tryToShowAd(
        activity: Activity,
        adPlaceName: String? = null,
        onReadyShowAd: (() -> Unit)? = null
    ) {
        when (getCurrentProvider()) {
            InterstitialAdProvider.ADMOB -> admobManager.tryToShowAd(activity, adPlaceName, onReadyShowAd)
            InterstitialAdProvider.TRADPLUS -> tradplusManager.tryToShowAd(activity, adPlaceName, onReadyShowAd)
        }
    }

    val adLoadingStateEventFlow: EventFlow<*>
        get() = when (getCurrentProvider()) {
            InterstitialAdProvider.ADMOB -> admobManager.adLoadingStateEventFlow
            InterstitialAdProvider.TRADPLUS -> tradplusManager.adLoadingStateEventFlow
        }

    val adShowStateEventFlow: EventFlow<*>
        get() = when (getCurrentProvider()) {
            InterstitialAdProvider.ADMOB -> admobManager.adShowStateEventFlow
            InterstitialAdProvider.TRADPLUS -> tradplusManager.adShowStateEventFlow
        }

    val adShowStateFlow: MutableStateFlow<Boolean>
        get() = when (getCurrentProvider()) {
            InterstitialAdProvider.ADMOB -> admobManager.adShowStateFlow
            InterstitialAdProvider.TRADPLUS -> tradplusManager.adShowStateFlow
        }
}