package dev.step.app.ui.dialog.redeemed

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.ad.InterstitialAdFacade
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.NativeAdPlace
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.ui.common.AppDefWithCloseDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import org.koin.compose.koinInject

@Composable
fun CouponRedeemedSuccessDialog(
    navUp: () -> Unit,

) {
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    val interstitialAdFacade: InterstitialAdFacade = koinInject()
    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    LaunchedEffect(Unit) {
        globalMainActivity?.let {
            interstitialAdFacade.tryToLoadAd(it)
        }
    }

//    val _navUp = {
//        navUp()
//        interstitialAdHelper.tryToShowAd("exit_dialog_redeem")
//    }

    AppDefWithCloseDialog(
        onDismiss = {
            OnBack()
            logEventRecord("exit_dialog_redeem")
        },
        onClose = {
            OnBack()
            logEventRecord("exit_dialog_redeem")
        },
        onConfirm = OnBack,
        confirmText = stringResource(id = R.string.text_ok),
        topPainter = painterResource(id = R.drawable.img_bigmoji_like),
        adPlace = NativeAdPlace.Dialog,
        adPlaceName = "redeem_success"
    ) {
        Column(
            modifier = Modifier
                .bodyWidth()
                .padding(horizontal = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            BlankSpacer(height = 32.dp)

            Text(
                text = stringResource(R.string.coupon_redeemed_successfully_title),
                fontSize = 20.sp
            )

            BlankSpacer(height = 14.dp)

            Text(
                text = stringResource(R.string.coupon_redeemed_successfully_tips),
                fontSize = 15.sp,
            )

            BlankSpacer(height = 20.dp)
        }
    }
}

@Preview
@Composable
private fun CouponRedeemedSuccessDialogPreview() {
    AppTheme {
        CouponRedeemedSuccessDialog(navUp = { /*TODO*/ })
    }
}