package dev.step.app.ui.screen.splash

import android.app.Activity
import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.ad.AppOpenAdFacade
import dev.step.app.androidplatform.biz.ad.InterstitialAdFacade
import dev.step.app.androidplatform.biz.ad.AdLoadingStateEvent
import dev.step.app.androidplatform.biz.ad.AdShowStateEvent
import dev.step.app.androidplatform.ext.findActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class SplashViewModel(
    private val appOpenAdFacade: AppOpenAdFacade,
    private val interstitialAdFacade: InterstitialAdFacade,
) : ViewModel(), ContainerHost<SplashViewState, SplashSideEffect> {

    override val container: Container<SplashViewState, SplashSideEffect> =
        container(SplashViewState())

    private var hasRequestConsentAndConfigureAds = false

    fun requestConsentAndConfigureAds(
        activity: Activity,
    ) {
        debugLog(tag = "SplashViewModel") { "requestConsentAndConfigureAds() hasRequestConsentAndConfigureAds: $hasRequestConsentAndConfigureAds" }
        if (hasRequestConsentAndConfigureAds) return

        registerAdFlow()
        onLoadAds(activity)

        hasRequestConsentAndConfigureAds = true
    }

    private fun registerAdFlow() {
        configureAdmobAppOpenAd()
    }

    private fun onLoadAds(
        context: Context,
    ) {
        viewModelScope.launch(Dispatchers.Main.immediate) {

            interstitialAdFacade.tryToLoadAd(context.findActivity())

            appOpenAdFacade.tryToShowAd(context.findActivity())
        }
    }

    private fun configureAdmobAppOpenAd() {
        appOpenAdFacade.adLoadingStateEventFlow.onEach { event ->
            debugLog(tag = "SplashViewModel") { "AdLoadingStateEvent: $event" }
            when (event) {
                AppOpenAdLoadingStateEvent.FailedToLoad -> {
                    delay(1000)
                    intent { postSideEffect(SplashSideEffect.OnNavUp) }
                }
                AppOpenAdLoadingStateEvent.TimeOut -> {
                    intent { postSideEffect(SplashSideEffect.OnNavUp) }
                }
                AppOpenAdLoadingStateEvent.Loaded -> {
                    onShowAdmobAppOpenAd()
                }
            }
        }.launchIn(viewModelScope)

        appOpenAdFacade.adShowStateEventFlow.onEach { event ->
            debugLog(tag = "SplashViewModel") { "AdShowStateEvent: $event" }
            when (event) {
                AppOpenAdShowStateEvent.FailedToShow, AppOpenAdShowStateEvent.Finish -> {
                    intent { postSideEffect(SplashSideEffect.OnNavUp) }
                }
                AppOpenAdShowStateEvent.SkipToShow -> {
                    delay(1000)
                    intent { postSideEffect(SplashSideEffect.OnNavUp) }
                }
                AppOpenAdShowStateEvent.Showing -> {
                    intent {
                        delay(500)
                        reduce { state.copy(appOpenAdShowing = true) }
                    }
                }
            }
        }.launchIn(viewModelScope)
    }

    private fun onShowAdmobAppOpenAd() = intent {
        postSideEffect(SplashSideEffect.ShowAdmobAppOpenAd)
    }

}