package dev.step.app.ui.dialog.rewarded

import android.annotation.SuppressLint
import android.app.Activity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.RewardedDialogNode
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.androidcomponent.global.showToast
import dev.step.app.androidplatform.biz.ad.InterstitialAdFacade
import dev.step.app.androidplatform.biz.ad.admob.rewarded.AdmobRewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.send
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.dialog.rewardedloading.rewardedLoadingDialogTimeoutEventFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

sealed interface RewardedDialogSideEffect {
    data object NavUp : RewardedDialogSideEffect
}


@KoinViewModel
class RewardedDialogViewModel(
    private val rewardedDialogArgs: RewardedDialogNode.RewardedDialogArgs,
    private val interstitialAdFacade: InterstitialAdFacade,
    private val admobRewardedAdManager: AdmobRewardedAdManager,
    val walletBizKv: WalletBizKv,
) : ViewModel(), ContainerHost<RewardedDialogViewState, RewardedDialogSideEffect> {

    override val container: Container<RewardedDialogViewState, RewardedDialogSideEffect> =
        container(RewardedDialogViewState.Empty)

    val from = rewardedDialogArgs.from
    val coins = rewardedDialogArgs.coins
    val times = rewardedDialogArgs.times

    init {
        globalMainActivity?.let { activity ->
            interstitialAdFacade.tryToLoadAd(activity)
            admobRewardedAdManager.tryToLoadAd(activity)
        }

        rewardedLoadingDialogTimeoutEventFlow.onEach {
            doClickGetCoinsMultiply(false)
        }.launchIn(viewModelScope)

        rewardedLoadingDialogFinishEventFlow.onEach {
            val addCoins = coins * times

            walletBizKv.setCoinBalance(
                walletBizKv.getCoinBalance() + addCoins
            )

            intent {
                postSideEffect(RewardedDialogSideEffect.NavUp)
            }
        }.launchIn(viewModelScope)

//        interstitialAdHelper.interstitialAdEventFlow.onEach { adShowEvent ->
//            if (adShowEvent is MaxInterstitialAdShowEvent.Skip || adShowEvent is MaxInterstitialAdShowEvent.Showing) {
//
//                walletBizKv.setCoinBalance(
//                    walletBizKv.getCoinBalance() + coins
//                )
//
//                intent {
//                    postSideEffect(RewardedDialogSideEffect.NavUp)
//                }
//                logEventRecord("exit_dialog_rewarded")
//            }
//        }.launchIn(viewModelScope)
    }


//    fun onClose() = intent {
//        interstitialAdHelper.tryToShowAd("exit_${from}")
//    }

    fun doClickGetCoinsMultiply(clicked: Boolean = true) = intent {
        reduce {
            state.copy(hasClickGetCoinsMultiply = clicked)
        }
    }
}