package dev.step.app.ui.screen.splash

import android.annotation.SuppressLint
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.ProgressIndicatorDefaults
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.backstack.NavBackHandler
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.PendingIntentPassedToIntentExtra
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.notification.HandsUpNotification
import dev.step.app.androidplatform.biz.ad.AppOpenAdFacade
import dev.step.app.androidplatform.biz.ad.appopen.closeSplashEventFlow
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.screen.steps.backToStepsScreenDoNotShowInterAd
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Composable
fun SplashScreen(
    isColdStart: Boolean,
    navUp: () -> Unit,
    onNext: () -> Unit,
) {
    val context = LocalContext.current
    val viewModel: SplashViewModel = koinViewModel()
    val viewState by viewModel.collectAsState()

    LaunchedEffect(Unit) {
        debugLog("SplashAd SplashAdScreen()")
        viewModel.requestConsentAndConfigureAds(context.findActivity())
    }

    SplashScreen(
        isColdStart = isColdStart,
        viewState = viewState,
        viewModel = viewModel,
        navUp = navUp,
        onNext = onNext,
    )
}

@Composable
fun SplashScreen(
    isColdStart: Boolean,
    viewState: SplashViewState,
    viewModel: SplashViewModel,
    navUp: () -> Unit,
    onNext: () -> Unit,
) {
    val context = LocalContext.current
    val appOpenAdFacade: AppOpenAdFacade = koinInject()

    NavBackHandler(true) {

    }

    val navAction = if (isColdStart) onNext else navUp

    LaunchedEffect(Unit) {
        debugLog("SplashAdScreen isColdStart: $isColdStart")

        closeSplashEventFlow.onEach {
            debugLog("loadAd receiver closeSplashEvent: $it")
            launch(Dispatchers.Main) {
                debugLog("loadAd splash progress = 1f")
            }
            backToStepsScreenDoNotShowInterAd.emit(Unit)
        }.launchIn(this)
    }


    val showFinishedBlock = {
        debugLog("loadAd SplashAdScreenContent loadingFinished")
        navAction()
        PendingIntentPassedToIntentExtra.handleIntent(isColdStart = isColdStart)
        HandsUpNotification.handleNotificationIntent()
    }

    viewModel.collectSideEffect {
        when (it) {
            SplashSideEffect.OnNavUp -> showFinishedBlock()

            SplashSideEffect.ShowAdmobAppOpenAd -> appOpenAdFacade.tryToShowAd(
                context.findActivity(),
                immediate = true
            )
        }
    }

//    LaunchedEffect(Unit) {
//        maxAppOpenAdHelper.tryToShowExecuteResultEventFlow.onEach {
//            showFinishedBlock()
//        }.launchIn(this)
//    }

    SplashScreenContent(viewState)
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
private fun SplashScreenContent(
    viewState: SplashViewState,
) {
    Scaffold(modifier = Modifier.fillMaxSize()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.bg_splash),
                contentDescription = null,
                modifier = Modifier.matchParentSize(),
                contentScale = ContentScale.Crop,
                alignment = Alignment.BottomCenter
            )

            Column(
                modifier = Modifier
                    .bodyWidth()
                    .padding(horizontal = 32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_app_launcher),
                    contentDescription = null,
                    modifier = Modifier.size(88.dp)
                )

                BlankSpacer(height = 30.dp)

                Text(
                    text = stringResource(id = R.string.app_name),
                    fontSize = 22.5.sp,
                    color = Color.White
                )

                BlankSpacer(height = 8.dp)

                Text(
                    text = stringResource(id = R.string.splash_content_0),
                    style = MaterialTheme.typography.body2.copy(
                        fontFamily = FontFamily(Font(R.font.gabarito_medium)),
                        color = Color.White,
                        fontSize = 12.5.sp,
                        textAlign = TextAlign.Center
                    )
                )
            }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .padding(horizontal = 56.dp)
                    .padding(bottom = 138.dp)
            ) {
//                Text(
//                    text = stringResource(R.string.text_this_action_might_contain_ad),
//                    style = MaterialTheme.typography.body2.copy(
//                        fontFamily = FontFamily(Font(R.font.gabarito_medium)),
//                        color = Color.White,
//                        fontSize = 12.sp,
//                    ),
//                    textAlign = TextAlign.Center,
//                    modifier = Modifier.fillMaxWidth(),
//                )
//
//                BlankSpacer(height = 6.dp)

                if (viewState.appOpenAdShowing) {
                    LinearProgressIndicator(
                        progress = 1f,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(14.dp)
                            .border(2.dp, color = Color.White, shape = CircleShape),
                        color = AppColor.Primary,
                        backgroundColor = Color.White,
                        strokeCap = StrokeCap.Round
                    )
                } else {
                    LinearProgressIndicator(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(14.dp)
                            .border(2.dp, color = Color.White, shape = CircleShape),
                        color = AppColor.Primary,
                        backgroundColor = Color.White,
                        strokeCap = StrokeCap.Round
                    )
                }
            }
        }
    }
}

@Preview(device = Devices.PIXEL_4)
@Composable
fun SplashAdScreenContentPreview() {
    AppTheme {
        SplashScreenContent(SplashViewState())
    }
}
