package dev.step.app.ui.dialog.stage1kstepsrewarded

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.RatingHelper
import dev.step.app.androidplatform.biz.ad.InterstitialAdFacade
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.todayStartInstant
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.common.BigBadgeDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.dialog.rewardedloading.rewardedLoadingDialogTimeoutEventFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import org.orbitmvi.orbit.viewmodel.container

@Composable
fun StageOneRewardedDialog(
    navUp: () -> Unit,
    remoteConfigHelper: FirebaseRemoteConfigHelper = koinInject(),
//    rewardedAdHelper: MaxRewardedAdHelper = koinInject(),
    rewardedAdManager: RewardedAdManager = koinInject(),
    ratingHelper: RatingHelper = koinInject(),
    viewModel: StageOneRewardedDialogViewModel = koinViewModel(),
) {
    val viewState by viewModel.collectAsState()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    viewModel.collectSideEffect {
        when (it) {
            is StageOneRewardedDialogSideEffect.NavUp -> {
                navUp()
                ratingHelper.tryToOpenReviewDialog()
            }
        }
    }

    val dailyTask = remoteConfigHelper.getDailyTask()

    val onClose = {
        val addCoins = dailyTask?.s1_rewarded_coins ?: 0

        viewModel.walletBizKv.setCoinBalance(
            viewModel.walletBizKv.getCoinBalance() + addCoins
        )

        OnBack()

        logEventRecord("exit_dialog_task1")
    }

    if (!viewState.hasClickGetCoinsMultiply) {
        BigBadgeDialog(
            onDismiss = onClose,
            onClose = onClose,
            onConfirm = {
                viewModel.doClickGetCoinsMultiply()
                rewardedAdManager.tryToShowRewardedLoadingDialog("task1bonus")

                logEventRecord("click_task1_bonus")
            },
            confirmText = stringResource(id = R.string.title_get_coins),
            singleRewardTimes = dailyTask?.s1_rewarded_multiplier ?: 1,
            bigBadgePainter = painterResource(id = R.drawable.img_badge_1k_steps),
            bigBadgeTitle = "+ ${dailyTask?.s1_rewarded_coins} ${stringResource(id = R.string.text_coins)}",
            adPlace = NativeAdPlace.Dialog,
            adPlaceName = "task1"
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                BlankSpacer(height = 22.dp)

                Text(
                    text = stringResource(id = R.string.title_completing_task_bonus),
                    fontSize = 20.sp
                )

                BlankSpacer(height = 32.dp)
            }
        }
    }
}

sealed interface StageOneRewardedDialogSideEffect {
    data object NavUp : StageOneRewardedDialogSideEffect
}

data class StageOneRewardedDialogViewState(
    val hasClickGetCoinsMultiply: Boolean = false
) {
    companion object {
        val Empty = StageOneRewardedDialogViewState()
    }
}

@KoinViewModel
class StageOneRewardedDialogViewModel(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val interstitialAdFacade: InterstitialAdFacade,
    val walletBizKv: WalletBizKv,
) : ViewModel(), ContainerHost<StageOneRewardedDialogViewState, StageOneRewardedDialogSideEffect> {

    override val container: Container<StageOneRewardedDialogViewState, StageOneRewardedDialogSideEffect> =
        container(StageOneRewardedDialogViewState.Empty)

    private val dailyTask = remoteConfigHelper.getDailyTask()

    init {

        rewardedLoadingDialogTimeoutEventFlow.onEach {
            doClickGetCoinsMultiply(false)
        }.launchIn(viewModelScope)

        globalMainActivity?.let { activity ->
            interstitialAdFacade.tryToLoadAd(activity)
        }

        viewModelScope.launch {
            val todayStartInstant = nowInstant().todayStartInstant()
            walletBizKv.doneStageOneTask(todayStartInstant)
        }

        rewardedLoadingDialogFinishEventFlow.onEach {
            val addCoins =
                (dailyTask?.s1_rewarded_coins ?: 0) * (dailyTask?.s1_rewarded_multiplier ?: 1)

            walletBizKv.setCoinBalance(
                walletBizKv.getCoinBalance() + addCoins
            )

            intent {
                postSideEffect(StageOneRewardedDialogSideEffect.NavUp)
            }
        }.launchIn(viewModelScope)

//        // work is fine
//        admobInterstitialAdManager.adShowStateEventFlow.onEach { adShowEvent ->
//            if (adShowEvent in listOf(
//                    AdmobInterstitialAdManager.AdShowStateEvent.SkipToShow,
//                    AdmobInterstitialAdManager.AdShowStateEvent.Showing
//                )
//            ) {
//                val addCoins = dailyTask?.s1_rewarded_coins ?: 0
//
//                walletBizKv.setCoinBalance(
//                    walletBizKv.getCoinBalance() + addCoins
//                )
//
//                intent {
//                    postSideEffect(StageOneRewardedDialogSideEffect.NavUp)
//                }
//                logEventRecord("exit_dialog_task1")
//            }
//
//        }.launchIn(viewModelScope)

//        interstitialAdHelper.interstitialAdEventFlow.onEach { adShowEvent ->
//            if (adShowEvent is MaxInterstitialAdShowEvent.Skip || adShowEvent is MaxInterstitialAdShowEvent.Showing) {
//
//                val addCoins = dailyTask?.s1_rewarded_coins ?: 0
//
//                walletBizKv.setCoinBalance(
//                    walletBizKv.getCoinBalance() + addCoins
//                )
//
//                intent {
//                    postSideEffect(StageOneRewardedDialogSideEffect.NavUp)
//                }
//                logEventRecord("exit_dialog_task1")
//            }
//        }.launchIn(viewModelScope)

    }

//    fun onClose() {
////        interstitialAdHelper.tryToShowAd("exit_dialog_task1")
//        viewModelScope.launch {
//            globalMainActivity?.let { activity ->
//                admobInterstitialAdManager.tryToShowAd(activity, "exit_dialog_task1")
//            }
//        }
//    }

    fun doClickGetCoinsMultiply(clicked: Boolean = true) = intent {
        reduce {
            state.copy(hasClickGetCoinsMultiply = clicked)
        }
    }
}

@Preview
@Composable
private fun StageOneRewardedDialogPreview() {
    StageOneRewardedDialog(
        navUp = {},
    )
}