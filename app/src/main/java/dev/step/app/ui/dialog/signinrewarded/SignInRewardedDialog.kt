package dev.step.app.ui.dialog.signinrewarded

import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.common.BigBadgeDialog
import dev.step.app.ui.common.BlankSpacer
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import org.koin.android.annotation.KoinViewModel
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.compose.collectSideEffect
import org.orbitmvi.orbit.viewmodel.container
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.ad.InterstitialAdFacade
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.NativeAdPlace
import dev.step.app.ui.dialog.rewardedloading.rewardedLoadingDialogTimeoutEventFlow
import dev.step.app.ui.theme.AppTheme
import org.orbitmvi.orbit.compose.collectAsState

@Composable
fun SignInRewardedDialog(
    navUp: () -> Unit,
    remoteConfigHelper: FirebaseRemoteConfigHelper = koinInject(),
//    rewardedAdHelper: MaxRewardedAdHelper = koinInject(),
    rewardedAdManager: RewardedAdManager = koinInject(),
    viewModel: SignInRewardedDialogViewModel = koinViewModel()
) {
    val viewState by viewModel.collectAsState()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    viewModel.collectSideEffect {
        when (it) {
            is SignInRewardedDialogSideEffect.NavUp -> navUp()
        }
    }

    val dailyTask = remoteConfigHelper.getDailyTask()

    val onClose = {
        val addCoins = dailyTask?.sign_in_rewarded_coins ?: 0

        viewModel.walletBizKv.setCoinBalance(
            viewModel.walletBizKv.getCoinBalance() + addCoins
        )

        OnBack()
        logEventRecord("exit_dialog_checkin")
    }

    if (!viewState.hasClickGetCoinsMultiply) {
        BigBadgeDialog(
            onDismiss = onClose,
            onClose = onClose,
            onConfirm = {
                viewModel.doClickGetCoinsMultiply()

                rewardedAdManager.tryToShowRewardedLoadingDialog("checkinbonus")
                logEventRecord("click_checkin_bonus")
            },
            confirmText = stringResource(id = R.string.title_get_coins),
            singleRewardTimes = dailyTask?.sign_in_rewarded_multiplier ?: 1,
            bigBadgePainter = painterResource(id = R.drawable.img_badge_sign_in),
            bigBadgeTitle = "+ ${dailyTask?.sign_in_rewarded_coins} ${stringResource(id = R.string.text_coins)}",
            adPlace = NativeAdPlace.Dialog,
            adPlaceName = "checkin"
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                BlankSpacer(height = 22.dp)

                Text(text = stringResource(R.string.check_in_text_check_in_bonus), fontSize = 20.sp)

                BlankSpacer(height = 32.dp)
            }
        }
    }
}

@Preview
@Composable
private fun SignInRewardedDialogPreview() {
    AppTheme {
        SignInRewardedDialog(navUp = { /*TODO*/ })
    }
}

sealed interface SignInRewardedDialogSideEffect {
    data object NavUp : SignInRewardedDialogSideEffect
}

data class SignInRewardedDialogViewState(
    val hasClickGetCoinsMultiply: Boolean = false
) {
    companion object {
        val Empty = SignInRewardedDialogViewState()
    }
}

@KoinViewModel
class SignInRewardedDialogViewModel(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val interstitialAdFacade: InterstitialAdFacade,
    val walletBizKv: WalletBizKv,
) : ViewModel(), ContainerHost<SignInRewardedDialogViewState, SignInRewardedDialogSideEffect> {

    override val container: Container<SignInRewardedDialogViewState, SignInRewardedDialogSideEffect> =
        container(SignInRewardedDialogViewState.Empty)

    private val dailyTask = remoteConfigHelper.getDailyTask()

    init {

        logEventRecord("show_dialog_checkin")

        rewardedLoadingDialogTimeoutEventFlow.onEach {
            doClickGetCoinsMultiply(false)
        }.launchIn(viewModelScope)

        globalMainActivity?.let { activity ->
            interstitialAdFacade.tryToLoadAd(activity)
        }

        viewModelScope.launch {
            val now = nowInstant()
            val dayOfWeek = now.toLocalDateTime(TimeZone.currentSystemDefault()).dayOfWeek
            walletBizKv.doSignIn(dayOfWeek)
        }

        rewardedLoadingDialogFinishEventFlow.onEach {
            val addCoins =
                (dailyTask?.sign_in_rewarded_coins ?: 0) * (dailyTask?.sign_in_rewarded_multiplier
                    ?: 1)

            walletBizKv.setCoinBalance(
                walletBizKv.getCoinBalance() + addCoins
            )

            intent {
                postSideEffect(SignInRewardedDialogSideEffect.NavUp)
            }
        }.launchIn(viewModelScope)

//        interstitialAdHelper.interstitialAdEventFlow.onEach { adShowEvent ->
//            debugLog("_navUp MaxInterstitialAdShowEvent: $adShowEvent")
//            if (adShowEvent is MaxInterstitialAdShowEvent.Skip || adShowEvent is MaxInterstitialAdShowEvent.Showing) {
//
//                val addCoins = dailyTask?.sign_in_rewarded_coins ?: 0
//
//                walletBizKv.setCoinBalance(
//                    walletBizKv.getCoinBalance() + addCoins
//                )
//
//                intent {
//                    postSideEffect(SignInRewardedDialogSideEffect.NavUp)
//                }
//                logEventRecord("exit_dialog_checkin")
//            }
//        }.launchIn(viewModelScope)
    }

//    fun onClose() = intent {
//        interstitialAdHelper.tryToShowAd("exit_dialog_checkin")
//    }

    fun doClickGetCoinsMultiply(clicked: Boolean = true) = intent {
        reduce {
            state.copy(hasClickGetCoinsMultiply = clicked)
        }
    }
}