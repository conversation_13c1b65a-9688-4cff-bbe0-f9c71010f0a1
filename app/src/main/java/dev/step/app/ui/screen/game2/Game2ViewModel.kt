@file:Suppress("ObjectPropertyName")

package dev.step.app.ui.screen.game2

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.Game2Node
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.InterstitialAdFacade
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.biz.game.GameADT
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.todayStartInstant
import dev.step.app.androidplatform.randomSelection
import dev.step.app.androidplatform.randomSort
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.kvstore.WalletBizKv
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container
import kotlin.random.Random
import kotlin.random.nextInt

private val _slotItemPatterns by lazy {
    listOf(
        SlotItemPattern(R.drawable.img_sm_blue),
        SlotItemPattern(R.drawable.img_sm_green),
        SlotItemPattern(R.drawable.img_sm_red),
        SlotItemPattern(R.drawable.img_sm_purple),
        SlotItemPattern(R.drawable.img_sm_yellow),
    )
}

@KoinViewModel
class Game2ViewModel(
    private val args: Game2Node.Game2Args,
//    private val rewardedAdHelper: MaxRewardedAdHelper,
    private val rewardedAdManager: RewardedAdManager,
    private val interstitialAdFacade: InterstitialAdFacade,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val walletBizKv: WalletBizKv,
    private val operateDataKv: UserOperateDataKv,
) : ViewModel(), ContainerHost<Game2ViewState, Game2SideEffect> {

    override val container: Container<Game2ViewState, Game2SideEffect> =
        container(Game2ViewState.Empty)

    init {
        globalMainActivity?.let { activity ->
            interstitialAdFacade.tryToLoadAd(activity)
        }

        launchingAndPlay()
    }

    private fun launchingAndPlay() = intent {
        val navFromNotification = args.navFromNotification

        val delayMillis = if (navFromNotification) 900L else 620L
        delay(delayMillis)

        runCatching {
            onPlay()
        }
    }


    fun onRefresh() = intent {
        val freeSpins = remoteConfigHelper.getGame2() ?: return@intent

        val timeLimit = freeSpins.play_times

        val todayStartSeconds = nowInstant().todayStartInstant().epochSeconds
        val remaining = walletBizKv.getGame2Remaining(todayStartSeconds, timeLimit)

        reduce {
            state.copy(
                remaining = remaining,
                withdrawEnable = !operateDataKv.tenjinAttr.isOrganic(),
            )
        }
    }

    private var refreshTimes: Int = 0
    fun onRefreshCoins() = intent {
        if (refreshTimes > 0) {
            delay(1000)
            refreshTimes++
        }

        reduce {
            state.copy(
                walletCoins = walletBizKv.getCoinBalance()
            )
        }
    }

    fun onPlay() = intent {
        if (state.isPlaying) return@intent

        val now = nowInstant().todayStartInstant()

        val remainingLimit = remoteConfigHelper.getGame2()?.play_times ?: 5

        if (walletBizKv.getGame2Remaining(
                now.epochSeconds,
                remainingLimit
            ).remainingTimes > 0
        ) {
            logEventRecord("click_game2_start")

            reduce {
                state.copy(isPlaying = true)
            }

            val selectionIndexes = doPatternSelectionIndexes()

            postSideEffect(Game2SideEffect.DoPlay(selectionIndexes))

        } else {
            postSideEffect(Game2SideEffect.ToNoRemainingTimeDialog(GameADT.Game2))
        }
    }

    private var rewardedAdJob: Job? = null
    fun onPlayFinish() = intent {

        rewardedAdManager.tryToShowRewardedLoadingDialog("game2_finish")

        reduce {
            state.copy(isPlaying = false)
        }

        rewardedAdJob?.cancel()
        rewardedAdJob = null

        rewardedAdJob = viewModelScope.launch {
            rewardedLoadingDialogFinishEventFlow.take(1).collect {
                val freeSpins = remoteConfigHelper.getGame2()

                val awardCoins = freeSpins?.award_coins ?: 0
                val awardTimes = freeSpins?.award_multiplier ?: 1

                postSideEffect(
                    Game2SideEffect.ToRewardedDialog(
                        gameADT = GameADT.Game2,
                        coins = awardCoins,
                        times = awardTimes,
                    )
                )

                walletBizKv.spendOnceGame2Remaining()

                val remainingLimit = remoteConfigHelper.getGame2()?.play_times ?: 5

                reduce {
                    state.copy(
                        remaining = walletBizKv.getGame2Remaining(
                            nowInstant().todayStartInstant().epochSeconds,
                            remainingLimit
                        )
                    )
                }
            }
        }
    }

    private fun doPatternSelectionIndexes(): List<Int> {
        val selectionItems = doPatternSelection()
        return selectionItems.map {
            _slotItemPatterns.indexOf(it)
        }
    }

    private fun doPatternSelection(): List<SlotItemPattern> {
        val patternSelectionNum = Random.nextInt(2..4)

        val randomPatterns = _slotItemPatterns.randomSelection(patternSelectionNum)

        val slotItemPatternItems = mutableListOf<SlotItemPattern>()
        when (randomPatterns.size) {
            2 -> {
                val pattern0 = randomPatterns[0]
                val pattern1 = randomPatterns[1]

                val randomNi = Random.nextInt(0..1)

                if (randomNi == 0) { // use 3:1
                    repeat(3) { slotItemPatternItems.add(pattern0) }
                    repeat(1) { slotItemPatternItems.add(pattern1) }
                } else { // use 2:2
                    repeat(2) {
                        slotItemPatternItems.add(pattern0)
                        slotItemPatternItems.add(pattern1)
                    }
                }
            }

            3 -> {
                val pattern0 = randomPatterns[0]
                val pattern1 = randomPatterns[1]
                val pattern2 = randomPatterns[2]

                repeat(2) { slotItemPatternItems.add(pattern0) }
                repeat(1) {
                    slotItemPatternItems.add(pattern1)
                    slotItemPatternItems.add(pattern2)
                }

            }

            4 -> {
                val pattern0 = randomPatterns[0]
                val pattern1 = randomPatterns[1]
                val pattern2 = randomPatterns[2]
                val pattern3 = randomPatterns[3]

                slotItemPatternItems.apply {
                    add(pattern0)
                    add(pattern1)
                    add(pattern2)
                    add(pattern3)
                }
            }

            else -> throw IllegalStateException("impossible random patterns size: ${randomPatterns.size}")

        }

        return slotItemPatternItems.randomSort()
    }
}