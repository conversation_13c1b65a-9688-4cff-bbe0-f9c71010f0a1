# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Build Commands
```bash
# Clean build
./gradlew clean

# Build debug version
./gradlew assembleDebug

# Build internal release (with minification)
./gradlew assembleInternal

# Generate AAB for Play Store
./gradlew bundleInternal
```

### Custom Project Commands
```bash
# Mirror project to remote directory (from mirrorToRemoteDir.sh)
./gradlew clean
./gradlew replaceProjectPackageName
./gradlew replaceProjectPackageAndDirName
./gradlew mirrorToRemoteDir
```

## Architecture Overview

This is an Android step tracking app built with modern Android architecture patterns:

### Core Architecture
- **UI**: Jetpack Compose with MVI pattern using Orbit MVI
- **Dependency Injection**: Koin with KSP annotations
- **Navigation**: Guia library (not Navigation Compose)
- **Data Layer**: Room database + MMKV key-value store
- **State Management**: Centralized ViewModels with clear state/side effects separation

### Key Components

#### Data Layer
- `StepAppDB` - Room database with single entity for step tracking records
- `StepsTrackRecordRepo` - Repository pattern for data access
- MMKV for user settings and temporary data storage
- Firebase Remote Config for dynamic configuration

#### Business Logic
- `StepTrackingSession` - Core step detection and recording logic
- Sensor abstraction layer supporting both hardware step detector and accelerometer
- Advertisement management (AdMob rewarded/interstitial ads)
- Analytics integration (Firebase, Tenjin SDK)

#### UI Structure
- Home screen with bottom navigation (Steps, Reports, Profile, Wallet)
- Extensive dialog system for user interactions
- Game screens with mini-games integration
- MVI pattern: ViewModels implement `ContainerHost<ViewState, SideEffect>`

## Package Structure Guide

### Main Package: `dev.step.app`
- `androidplatform/` - Android platform abstractions and services
  - `androidcomponent/` - Android-specific components (notifications, sensors, services)
  - `biz/` - Business logic layer (ads, analytics, Firebase integration)
  - `ext/` - Extension functions and utilities
  - `memorystore/` - In-memory data management
- `data/` - Data layer following repository pattern
  - `adt/` - Algebraic data types and enums
  - `db/` - Room database configuration and entities
  - `kvstore/` - MMKV key-value storage
  - `pojo/` - Plain old Java objects and data classes
  - `repo/` - Repository implementations
- `ui/` - Jetpack Compose UI layer
  - `common/` - Reusable UI components
  - `dialog/` - Dialog components for various interactions
  - `screen/` - Main screens following MVI pattern
  - `theme/` - App theming and design system

### Project Structure

### Dependency Injection Modules
- `AndroidComponentModule` - Platform components
- `BizModule` - Business logic
- `DbModule` + `DbDaoModule` - Database setup
- `KvStoreModule` - Key-value storage
- `RepoModule` - Repository implementations

### Build Configuration
- Uses custom build configuration from `conf4build/buildConf.properties`
- Supports multiple build flavors (internal with ProGuard enabled)
- Multi-language support (Indonesian, Japanese, Portuguese, Russian, Turkish)

## Important Files for New Developers

1. `app/src/main/java/dev/step/app/AppInitializer.kt` - App initialization and setup
2. `app/src/main/java/dev/step/app/AppNavigation.kt` - Navigation structure and routing
3. `app/src/main/java/dev/step/app/data/repo/StepsTrackRecordRepo.kt` - Primary data access
4. `app/src/main/java/dev/step/app/androidplatform/memorystore/StepTrackingSession.kt` - Step tracking logic
5. `app/src/main/java/dev/step/app/ui/screen/steps/StepsViewModel.kt` - Main screen business logic

## Development Notes

- Step tracking uses real-time sensor data with hourly persistence
- Ad integration is extensive - understand the revenue model before modifying
- State management follows strict MVI pattern - keep ViewModels focused on orchestration
- Custom navigation system using Guia - not standard Navigation Compose
- Testing infrastructure is minimal and could be expanded